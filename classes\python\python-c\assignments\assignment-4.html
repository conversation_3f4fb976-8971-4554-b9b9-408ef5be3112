<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập 4: <PERSON><PERSON> v<PERSON>/<PERSON><PERSON><PERSON> - <PERSON> C</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .timer-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }

        .timer-info h3 {
            margin: 0;
            color: #FFD700;
        }

        .timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFD700;
        }

        .quiz-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 215, 0, 0.3);
        }

        .question-number {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .question-text {
            font-size: 1.1rem;
            margin: 20px 0;
            line-height: 1.6;
            white-space: pre-line;
        }

        .options-container {
            margin: 20px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .option:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: #FFD700;
            transform: translateX(5px);
        }

        .option.selected {
            background: rgba(255, 215, 0, 0.2);
            border-color: #FFD700;
            color: #FFD700;
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .option.incorrect {
            background: rgba(244, 67, 54, 0.2);
            border-color: #f44336;
            color: #f44336;
        }

        .option-letter {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .option.selected .option-letter {
            background: #FFD700;
            color: #333;
        }

        .option.correct .option-letter {
            background: #4CAF50;
            color: white;
        }

        .option.incorrect .option-letter {
            background: #f44336;
            color: white;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 15px;
        }

        .nav-button {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .nav-button:disabled {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .submit-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .submit-button:hover {
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .explanation {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            color: #81C784;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #FFD700, #FFA500);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .results-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .score-display {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin: 20px 0;
        }

        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .score-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .score-item h4 {
            color: #FFD700;
            margin-bottom: 10px;
        }

        .back-button {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
            text-decoration: none;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .assignment-container {
                margin: 100px auto 30px;
                padding: 0 15px;
            }

            .assignment-header h1 {
                font-size: 1.5rem;
            }

            .timer-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .navigation-buttons {
                flex-direction: column;
            }

            .nav-button {
                width: 100%;
            }

            .score-breakdown {
                grid-template-columns: 1fr;
            }
        }

        /* Loading and disabled states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .quiz-container.completed {
            border-color: #4CAF50;
        }

        .quiz-container.completed .question-header {
            border-bottom-color: #4CAF50;
        }

        /* Anti-cheat warning */
        .warning-message {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #ff6b6b;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../../index.html">Lớp Học</a></li>
                    <li><a href="../../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../../research/">Sự kiện</a></li>
                    <li><a href="../../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Animated Background Elements -->
    <div class="shooting-star" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; left: 30%; animation-delay: 1s;"></div>
    <div class="shooting-star" style="top: 60%; left: 50%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 15%; left: 80%; animation-delay: 0.5s;"></div>
    <div class="particle" style="top: 35%; left: 20%; animation-delay: 1.5s;"></div>
    <div class="particle" style="top: 75%; left: 70%; animation-delay: 2.5s;"></div>

    <div class="assignment-container">
        <!-- Assignment Header -->
        <div class="assignment-header">
            <h1><i class="fas fa-calculator"></i> Bài Tập 4: Toán Tử và Nhập/Xuất Dữ Liệu</h1>
            <p>Kiểm tra kiến thức về toán tử số học, hàm input()/print(), và ép kiểu dữ liệu</p>
        </div>

        <!-- Timer and Info -->
        <div class="timer-info">
            <div>
                <h3><i class="fas fa-clock"></i> Thời gian làm bài</h3>
                <p>75 phút (50 câu hỏi trắc nghiệm)</p>
            </div>
            <div class="timer" id="timer">75:00</div>
        </div>

        <!-- Warning Message -->
        <div class="warning-message" id="warningMessage" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Cảnh báo:</strong> Hệ thống đã phát hiện bạn chuyển tab/cửa sổ. 
            Hành vi này có thể được coi là gian lận. Vui lòng tập trung làm bài!
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar" style="width: 0%;"></div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="question-header">
                <div class="question-number" id="questionNumber">Câu 1/50</div>
                <div style="color: #FFD700;">
                    <i class="fas fa-star"></i> Điểm: <span id="currentScore">0</span>/100
                </div>
            </div>

            <div class="question-text" id="questionText">
                Đang tải câu hỏi...
            </div>

            <div class="options-container" id="optionsContainer">
                <!-- Options will be populated by JavaScript -->
            </div>

            <div class="explanation" id="explanation">
                <!-- Explanation will be shown here -->
            </div>

            <div class="navigation-buttons">
                <button class="nav-button" id="prevButton" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>
                <button class="nav-button submit-button" id="submitButton" onclick="submitQuiz()" style="display: none;">
                    <i class="fas fa-check"></i> Nộp bài
                </button>
                <button class="nav-button" id="nextButton" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <h2><i class="fas fa-trophy"></i> Kết Quả Bài Tập</h2>
            <div class="score-display" id="finalScore">0/100</div>
            
            <div class="score-breakdown">
                <div class="score-item">
                    <h4><i class="fas fa-check-circle"></i> Câu đúng</h4>
                    <p id="correctCount">0/50</p>
                </div>
                <div class="score-item">
                    <h4><i class="fas fa-times-circle"></i> Câu sai</h4>
                    <p id="incorrectCount">0/50</p>
                </div>
                <div class="score-item">
                    <h4><i class="fas fa-clock"></i> Thời gian</h4>
                    <p id="timeSpent">0 phút</p>
                </div>
                <div class="score-item">
                    <h4><i class="fas fa-percentage"></i> Tỷ lệ đúng</h4>
                    <p id="accuracy">0%</p>
                </div>
            </div>

            <a href="../python-c.html" class="back-button">
                <i class="fas fa-arrow-left"></i> Quay lại lớp học
            </a>
        </div>
    </div>

    <!-- Load Quiz Data -->
    <script src="quiz-data-4.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, setDoc, getDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Quiz state variables
        let currentQuestionIndex = 0;
        let userAnswers = [];
        let startTime = Date.now();
        let timeLimit = 75 * 60 * 1000; // 75 minutes in milliseconds
        let timerInterval;
        let quizCompleted = false;
        let currentUser = null;
        let isAdmin = false;
        let tabSwitchCount = 0;

        // Check authentication and load quiz
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                currentUser = user;

                // Check if user is admin
                try {
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    const userData = userDoc.data();
                    isAdmin = userData && (userData.isAdmin || user.email === '<EMAIL>');
                } catch (error) {
                    console.error('Error checking admin status:', error);
                }

                // Check if assignment already completed (unless admin)
                if (!isAdmin) {
                    try {
                        const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-4"));
                        if (assignmentDoc.exists()) {
                            // Assignment already completed, show results
                            const assignmentData = assignmentDoc.data();
                            showCompletedResults(assignmentData);
                            return;
                        }
                    } catch (error) {
                        console.error('Error checking assignment status:', error);
                    }
                }

                // Initialize quiz
                initializeQuiz();
            } else {
                // Redirect to login if not authenticated
                window.location.href = '../../../../auth/';
            }
        });

        function initializeQuiz() {
            // Initialize user answers array
            userAnswers = new Array(quizData.length).fill(null);

            // Start timer
            startTimer();

            // Load first question
            loadQuestion();

            // Set up anti-cheat detection
            setupAntiCheat();
        }

        function startTimer() {
            const timerElement = document.getElementById('timer');
            let timeRemaining = timeLimit;

            timerInterval = setInterval(() => {
                if (quizCompleted) {
                    clearInterval(timerInterval);
                    return;
                }

                timeRemaining -= 1000;

                if (timeRemaining <= 0) {
                    clearInterval(timerInterval);
                    autoSubmitQuiz();
                    return;
                }

                const minutes = Math.floor(timeRemaining / 60000);
                const seconds = Math.floor((timeRemaining % 60000) / 1000);
                timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // Change color when time is running low
                if (timeRemaining < 5 * 60 * 1000) { // Less than 5 minutes
                    timerElement.style.color = '#ff6b6b';
                }
            }, 1000);
        }

        function loadQuestion() {
            const question = quizData[currentQuestionIndex];

            // Update question number and progress
            document.getElementById('questionNumber').textContent = `Câu ${currentQuestionIndex + 1}/${quizData.length}`;
            document.getElementById('progressBar').style.width = `${((currentQuestionIndex + 1) / quizData.length) * 100}%`;

            // Load question text
            document.getElementById('questionText').textContent = question.question;

            // Load options
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'option';
                optionElement.onclick = () => selectOption(index);

                const letter = String.fromCharCode(65 + index); // A, B, C, D
                optionElement.innerHTML = `
                    <div class="option-letter">${letter}</div>
                    <div>${option}</div>
                `;

                // Mark as selected if user has already answered
                if (userAnswers[currentQuestionIndex] === index) {
                    optionElement.classList.add('selected');
                }

                optionsContainer.appendChild(optionElement);
            });

            // Update navigation buttons
            updateNavigationButtons();

            // Hide explanation
            document.getElementById('explanation').classList.remove('show');
        }

        function selectOption(optionIndex) {
            // Remove previous selection
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // Mark new selection
            document.querySelectorAll('.option')[optionIndex].classList.add('selected');

            // Save answer
            userAnswers[currentQuestionIndex] = optionIndex;

            // Update navigation buttons
            updateNavigationButtons();
        }

        function updateNavigationButtons() {
            const prevButton = document.getElementById('prevButton');
            const nextButton = document.getElementById('nextButton');
            const submitButton = document.getElementById('submitButton');

            // Previous button
            prevButton.disabled = currentQuestionIndex === 0;

            // Next/Submit button
            if (currentQuestionIndex === quizData.length - 1) {
                nextButton.style.display = 'none';
                submitButton.style.display = 'inline-block';
            } else {
                nextButton.style.display = 'inline-block';
                submitButton.style.display = 'none';
            }
        }

        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                loadQuestion();
            }
        }

        function nextQuestion() {
            if (currentQuestionIndex < quizData.length - 1) {
                currentQuestionIndex++;
                loadQuestion();
            }
        }

        async function submitQuiz() {
            if (quizCompleted) return;

            // Confirm submission
            if (!confirm('Bạn có chắc chắn muốn nộp bài? Bạn sẽ không thể thay đổi câu trả lời sau khi nộp.')) {
                return;
            }

            quizCompleted = true;
            clearInterval(timerInterval);

            // Calculate results
            const results = calculateResults();

            // Save to Firebase (unless admin)
            if (!isAdmin && currentUser) {
                try {
                    await saveResults(results);
                } catch (error) {
                    console.error('Error saving results:', error);
                    alert('Có lỗi khi lưu kết quả. Vui lòng thử lại.');
                    return;
                }
            }

            // Show results
            showResults(results);
        }

        function autoSubmitQuiz() {
            alert('Hết thời gian! Bài tập sẽ được nộp tự động.');
            submitQuiz();
        }

        function calculateResults() {
            let correctCount = 0;
            const totalQuestions = quizData.length;

            userAnswers.forEach((answer, index) => {
                if (answer === quizData[index].correct) {
                    correctCount++;
                }
            });

            const score = Math.round((correctCount / totalQuestions) * 100);
            const timeSpent = Math.round((Date.now() - startTime) / 60000); // in minutes
            const accuracy = Math.round((correctCount / totalQuestions) * 100);

            return {
                score: score,
                correctCount: correctCount,
                incorrectCount: totalQuestions - correctCount,
                totalQuestions: totalQuestions,
                timeSpent: timeSpent,
                accuracy: accuracy,
                userAnswers: userAnswers,
                tabSwitchCount: tabSwitchCount,
                completedAt: new Date().toISOString()
            };
        }

        async function saveResults(results) {
            const assignmentData = {
                score: results.score,
                correctCount: results.correctCount,
                totalQuestions: results.totalQuestions,
                timeSpent: results.timeSpent,
                userAnswers: results.userAnswers,
                tabSwitchCount: results.tabSwitchCount,
                completedAt: results.completedAt,
                assignmentId: 'assignment-4',
                lessonTitle: 'Bài 4: Toán Tử và Nhập/Xuất Dữ Liệu'
            };

            await setDoc(doc(db, "users", currentUser.uid, "assignments", "assignment-4"), assignmentData);
        }

        function showResults(results) {
            // Hide quiz container
            document.getElementById('quizContainer').style.display = 'none';

            // Show results container
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.style.display = 'block';

            // Update result displays
            document.getElementById('finalScore').textContent = `${results.score}/100`;
            document.getElementById('correctCount').textContent = `${results.correctCount}/${results.totalQuestions}`;
            document.getElementById('incorrectCount').textContent = `${results.incorrectCount}/${results.totalQuestions}`;
            document.getElementById('timeSpent').textContent = `${results.timeSpent} phút`;
            document.getElementById('accuracy').textContent = `${results.accuracy}%`;

            // Show detailed answers
            showDetailedAnswers();
        }

        function showCompletedResults(assignmentData) {
            // Hide quiz container
            document.getElementById('quizContainer').style.display = 'none';

            // Show results container
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.style.display = 'block';

            // Update result displays
            document.getElementById('finalScore').textContent = `${assignmentData.score}/100`;
            document.getElementById('correctCount').textContent = `${assignmentData.correctCount}/${assignmentData.totalQuestions}`;
            document.getElementById('incorrectCount').textContent = `${assignmentData.totalQuestions - assignmentData.correctCount}/${assignmentData.totalQuestions}`;
            document.getElementById('timeSpent').textContent = `${assignmentData.timeSpent} phút`;
            document.getElementById('accuracy').textContent = `${Math.round((assignmentData.correctCount / assignmentData.totalQuestions) * 100)}%`;

            // Load user answers for detailed view
            userAnswers = assignmentData.userAnswers || [];
            showDetailedAnswers();
        }

        function showDetailedAnswers() {
            // Create detailed answers section
            const detailedSection = document.createElement('div');
            detailedSection.className = 'detailed-answers';
            detailedSection.innerHTML = '<h3 style="color: #FFD700; margin: 30px 0 20px 0;"><i class="fas fa-list"></i> Chi Tiết Câu Trả Lời</h3>';

            quizData.forEach((question, index) => {
                const userAnswer = userAnswers[index];
                const isCorrect = userAnswer === question.correct;

                const questionDiv = document.createElement('div');
                questionDiv.className = 'detailed-question';
                questionDiv.style.cssText = `
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid ${isCorrect ? '#4CAF50' : '#f44336'};
                    border-radius: 10px;
                    padding: 20px;
                    margin: 15px 0;
                `;

                const statusIcon = isCorrect ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i>' : '<i class="fas fa-times-circle" style="color: #f44336;"></i>';
                const statusText = isCorrect ? 'Đúng' : 'Sai';

                questionDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <strong style="color: #FFD700;">Câu ${index + 1}</strong>
                        <span style="color: ${isCorrect ? '#4CAF50' : '#f44336'};">${statusIcon} ${statusText}</span>
                    </div>
                    <div style="margin-bottom: 15px; white-space: pre-line;">${question.question}</div>
                    <div style="margin-bottom: 15px;">
                        ${question.options.map((option, optIndex) => {
                            let optionClass = '';
                            let optionStyle = 'padding: 8px 12px; margin: 5px 0; border-radius: 5px; ';

                            if (optIndex === question.correct) {
                                optionStyle += 'background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; color: #4CAF50;';
                            } else if (optIndex === userAnswer && userAnswer !== question.correct) {
                                optionStyle += 'background: rgba(244, 67, 54, 0.2); border: 1px solid #f44336; color: #f44336;';
                            } else {
                                optionStyle += 'background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.2);';
                            }

                            const letter = String.fromCharCode(65 + optIndex);
                            const icon = optIndex === question.correct ? ' ✓' : (optIndex === userAnswer && userAnswer !== question.correct ? ' ✗' : '');

                            return `<div style="${optionStyle}">${letter}. ${option}${icon}</div>`;
                        }).join('')}
                    </div>
                    <div style="background: rgba(33, 150, 243, 0.1); border: 1px solid rgba(33, 150, 243, 0.3); padding: 12px; border-radius: 8px; color: #81C784;">
                        <strong>Giải thích:</strong> ${question.explanation}
                    </div>
                `;

                detailedSection.appendChild(questionDiv);
            });

            document.getElementById('resultsContainer').appendChild(detailedSection);
        }

        function setupAntiCheat() {
            // Detect tab/window switching
            document.addEventListener('visibilitychange', function() {
                if (document.hidden && !quizCompleted) {
                    tabSwitchCount++;
                    showWarning();
                }
            });

            // Detect window blur (switching to another application)
            window.addEventListener('blur', function() {
                if (!quizCompleted) {
                    tabSwitchCount++;
                    showWarning();
                }
            });

            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });

            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable F12, Ctrl+Shift+I, Ctrl+U, etc.
                if (e.key === 'F12' ||
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.key === 'u') ||
                    (e.ctrlKey && e.shiftKey && e.key === 'C')) {
                    e.preventDefault();
                }
            });
        }

        function showWarning() {
            const warningElement = document.getElementById('warningMessage');
            warningElement.style.display = 'block';

            // Hide warning after 5 seconds
            setTimeout(() => {
                warningElement.style.display = 'none';
            }, 5000);
        }

        // Make functions global for onclick handlers
        window.selectOption = selectOption;
        window.previousQuestion = previousQuestion;
        window.nextQuestion = nextQuestion;
        window.submitQuiz = submitQuiz;
    </script>

    <script src="../../../../assets/js/script.js"></script>
</body>
</html>
