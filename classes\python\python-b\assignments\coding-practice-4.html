<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON>p Code - Bài 4: <PERSON><PERSON>/<PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/png" href="../../../../favicon.png">
    
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100px);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1200px;
            width: 100%;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            box-sizing: border-box;
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .problem-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .problem-card:hover {
            transform: translateY(-5px);
            border-color: #FFD700;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .problem-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .problem-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .difficulty {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .difficulty.easy {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .difficulty.medium {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .difficulty.hard {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .problem-description {
            color: #ccc;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .problem-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            margin-right: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 4px;
            transition: width 0.3s;
            width: 0%;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #FFD700;
            font-weight: bold;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            margin: 2% auto;
            padding: 30px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 15px;
        }

        .close:hover {
            color: #FFD700;
        }

        .modal-header {
            margin-bottom: 20px;
            padding-right: 40px;
        }

        .modal-title {
            color: #FFD700;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .code-editor {
            width: 100%;
            min-height: 300px;
            background: #2d3748;
            color: #e2e8f0;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: #FFD700;
        }

        .results-section {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .test-progress {
            margin-bottom: 20px;
        }

        .test-progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .test-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 10px;
            transition: width 0.5s;
            width: 0%;
        }

        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .test-case {
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .test-case.passed {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
        }

        .test-case.failed {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #FFD700;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .back-link:hover {
            text-decoration: underline;
            color: #FFA500;
        }

        .back-link i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; left: 30%; animation-delay: 1s;"></div>
    <div class="shooting-star" style="top: 60%; left: 50%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 80%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 30%; left: 60%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 50%; left: 40%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 3s;"></div>

    <div class="assignment-container">
        <a href="../python-b.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - B
        </a>

        <div class="assignment-header">
            <h1>Luyện Tập Code - Bài 4</h1>
            <p>Toán Tử và Nhập/Xuất Dữ Liệu</p>
            <p><strong>14 bài tập</strong> từ dễ đến khó với hệ thống testcase tự động</p>
        </div>

        <div class="problems-grid" id="problemsGrid">
            <!-- Problems will be loaded here -->
        </div>
    </div>

    <!-- Modal for coding practice -->
    <div id="codingModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header">
                <div class="modal-title" id="modalTitle"></div>
                <div id="modalDescription"></div>
                <div id="modalObjective"></div>
            </div>
            
            <textarea id="codeEditor" class="code-editor" placeholder="Nhập code Python của bạn vào đây..."></textarea>
            
            <div class="action-buttons">
                <button class="btn btn-primary" id="runTestsBtn">
                    <i class="fas fa-play"></i> Chạy Testcase
                </button>
                <button class="btn btn-secondary" id="loadSampleBtn">
                    <i class="fas fa-code"></i> Xem Code Mẫu
                </button>
                <button class="btn btn-secondary" id="resetCodeBtn">
                    <i class="fas fa-refresh"></i> Reset Code
                </button>
            </div>

            <div class="results-section" id="resultsSection">
                <div class="test-progress">
                    <div class="test-progress-bar">
                        <div class="test-progress-fill" id="testProgressFill"></div>
                    </div>
                    <div id="testProgressText">Sẵn sàng chạy testcase...</div>
                </div>
                <div class="test-results" id="testResults"></div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore-compat.js"></script>
    
    <!-- Load coding problems data -->
    <script src="coding-data-4.js"></script>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBOGKWnGZhWJzWqZWY8R8X8R8X8R8X8R8X",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentProblem = null;
        let userProgress = {};

        // Load user progress from Firebase
        async function loadUserProgress() {
            const user = auth.currentUser;
            if (!user) return;

            try {
                const doc = await db.collection('coding-practice').doc(user.uid).get();
                if (doc.exists) {
                    userProgress = doc.data().lesson4 || {};
                }
            } catch (error) {
                console.error('Error loading progress:', error);
            }
        }

        // Save user progress to Firebase
        async function saveUserProgress(problemId, score, code) {
            const user = auth.currentUser;
            if (!user) return;

            try {
                const currentScore = userProgress[problemId]?.score || 0;
                if (score > currentScore) {
                    userProgress[problemId] = {
                        score: score,
                        code: code,
                        timestamp: firebase.firestore.FieldValue.serverTimestamp()
                    };

                    await db.collection('coding-practice').doc(user.uid).set({
                        lesson4: userProgress
                    }, { merge: true });
                }
            } catch (error) {
                console.error('Error saving progress:', error);
            }
        }

        // Initialize the page
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                await loadUserProgress();
                renderProblems();
            } else {
                window.location.href = '../../../../auth.html';
            }
        });

        function renderProblems() {
            const grid = document.getElementById('problemsGrid');
            grid.innerHTML = '';

            codingProblems.forEach(problem => {
                const progress = userProgress[problem.id] || { score: 0 };
                const progressPercent = (progress.score / problem.testcases.length) * 100;

                const card = document.createElement('div');
                card.className = 'problem-card';
                card.onclick = () => openProblem(problem);

                const difficultyClass = problem.difficulty === 'Dễ' ? 'easy' : 
                                      problem.difficulty === 'Trung Bình' ? 'medium' : 'hard';

                card.innerHTML = `
                    <div class="problem-header">
                        <div class="problem-title">${problem.id}. ${problem.title}</div>
                        <div class="difficulty ${difficultyClass}">${problem.difficulty}</div>
                    </div>
                    <div class="problem-description">${problem.description}</div>
                    <div class="problem-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressPercent}%"></div>
                        </div>
                        <div class="progress-text">${progress.score}/${problem.testcases.length}</div>
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        function openProblem(problem) {
            currentProblem = problem;
            const modal = document.getElementById('codingModal');
            
            document.getElementById('modalTitle').textContent = `${problem.id}. ${problem.title}`;
            document.getElementById('modalDescription').innerHTML = `<strong>Mô tả:</strong> ${problem.description}`;
            document.getElementById('modalObjective').innerHTML = `<strong>Mục tiêu:</strong> ${problem.objective}`;
            
            // Load saved code if exists
            const savedCode = userProgress[problem.id]?.code || '';
            document.getElementById('codeEditor').value = savedCode;
            
            // Reset results
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('testProgressFill').style.width = '0%';
            document.getElementById('testProgressText').textContent = 'Sẵn sàng chạy testcase...';
            
            modal.style.display = 'block';
        }

        // Modal event listeners
        document.querySelector('.close').onclick = function() {
            document.getElementById('codingModal').style.display = 'none';
        }

        window.onclick = function(event) {
            const modal = document.getElementById('codingModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        document.getElementById('loadSampleBtn').onclick = function() {
            if (currentProblem) {
                document.getElementById('codeEditor').value = currentProblem.sampleCode;
            }
        }

        document.getElementById('resetCodeBtn').onclick = function() {
            document.getElementById('codeEditor').value = '';
        }

        document.getElementById('runTestsBtn').onclick = function() {
            if (currentProblem) {
                runTestcases();
            }
        }

        // Python code runner simulation
        function runTestcases() {
            const code = document.getElementById('codeEditor').value.trim();
            if (!code) {
                alert('Vui lòng nhập code trước khi chạy testcase!');
                return;
            }

            const resultsSection = document.getElementById('resultsSection');
            const progressFill = document.getElementById('testProgressFill');
            const progressText = document.getElementById('testProgressText');
            const testResults = document.getElementById('testResults');

            resultsSection.style.display = 'block';
            testResults.innerHTML = '';
            progressFill.style.width = '0%';

            let passedTests = 0;
            const totalTests = currentProblem.testcases.length;

            // Run tests with animation
            currentProblem.testcases.forEach((testcase, index) => {
                setTimeout(() => {
                    const result = runSingleTest(code, testcase, index + 1);
                    if (result.passed) passedTests++;

                    // Update progress
                    const progress = ((index + 1) / totalTests) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `Đã chạy ${index + 1}/${totalTests} testcase - ${passedTests} passed`;

                    // Add test result
                    const testDiv = document.createElement('div');
                    testDiv.className = `test-case ${result.passed ? 'passed' : 'failed'}`;
                    testDiv.innerHTML = `
                        <strong>Test ${index + 1}:</strong> ${result.passed ? '✅ PASS' : '❌ FAIL'}<br>
                        ${result.passed ? '' : `<small>Expected: ${result.expected}<br>Got: ${result.actual}</small>`}
                    `;
                    testResults.appendChild(testDiv);

                    // Save progress when all tests complete
                    if (index === totalTests - 1) {
                        saveUserProgress(currentProblem.id, passedTests, code);
                        setTimeout(() => {
                            renderProblems(); // Update the main grid
                        }, 500);
                    }
                }, index * 200); // Stagger the tests for animation effect
            });
        }

        function runSingleTest(code, testcase, testNumber) {
            try {
                // Simple Python code simulation
                let output = simulatePythonCode(code, testcase.input);
                const expected = testcase.expectedOutput;

                // Normalize output for comparison
                output = output.trim();
                const normalizedExpected = expected.trim();

                return {
                    passed: output === normalizedExpected,
                    expected: normalizedExpected,
                    actual: output
                };
            } catch (error) {
                return {
                    passed: false,
                    expected: testcase.expectedOutput,
                    actual: `Error: ${error.message}`
                };
            }
        }

        function simulatePythonCode(code, inputs) {
            // This is a simplified Python interpreter simulation
            // It handles basic operations for the given problems

            let inputIndex = 0;
            let output = [];
            let currentLine = '';

            // Replace input() calls with actual values
            let modifiedCode = code.replace(/input\s*\(\s*["']([^"']*)["']\s*\)/g, (match, prompt) => {
                if (inputIndex < inputs.length) {
                    return `"${inputs[inputIndex++]}"`;
                }
                return '""';
            });

            // Create a safe execution environment
            const pythonPrint = (...args) => {
                // Handle different print scenarios
                let printArgs = [...args];
                let sep = ' ';
                let end = '\n';

                // Check for sep and end parameters
                if (printArgs.length > 0) {
                    const lastArg = printArgs[printArgs.length - 1];
                    if (typeof lastArg === 'object' && lastArg !== null) {
                        if (lastArg.sep !== undefined) {
                            sep = lastArg.sep;
                            printArgs.pop();
                        }
                        if (lastArg.end !== undefined) {
                            end = lastArg.end;
                            printArgs.pop();
                        }
                    }
                }

                const result = printArgs.join(sep);

                if (end === '\n') {
                    if (currentLine) {
                        output.push(currentLine + result);
                        currentLine = '';
                    } else {
                        output.push(result);
                    }
                } else {
                    currentLine += result + end;
                }
            };

            // Handle print statements
            modifiedCode = modifiedCode.replace(/print\s*\(/g, 'pythonPrint(');

            // Handle f-strings (basic support)
            modifiedCode = modifiedCode.replace(/f["']([^"']*){([^}]+)}([^"']*)["']/g, (match, before, variable, after) => {
                return `"${before}" + String(${variable}) + "${after}"`;
            });

            // Handle basic Python operations
            modifiedCode = modifiedCode.replace(/(\w+)\s*\/\/\s*(\w+)/g, 'Math.floor($1 / $2)');
            modifiedCode = modifiedCode.replace(/(\w+)\s*\*\*\s*(\w+)/g, 'Math.pow($1, $2)');
            modifiedCode = modifiedCode.replace(/(\d+)\s*\*\*\s*(\d+)/g, 'Math.pow($1, $2)');
            modifiedCode = modifiedCode.replace(/int\s*\(/g, 'parseInt(');
            modifiedCode = modifiedCode.replace(/float\s*\(/g, 'parseFloat(');
            modifiedCode = modifiedCode.replace(/str\s*\(/g, 'String(');

            // Handle print with sep and end parameters
            modifiedCode = modifiedCode.replace(/pythonPrint\s*\(([^)]+),\s*sep\s*=\s*["']([^"']*)["']\s*\)/g,
                (match, args, sep) => {
                    const argList = args.split(',').map(arg => arg.trim());
                    return `pythonPrint(${argList.join(', ')}, {sep: "${sep}"})`;
                });

            modifiedCode = modifiedCode.replace(/pythonPrint\s*\(([^)]+),\s*end\s*=\s*["']([^"']*)["']\s*\)/g,
                (match, args, end) => {
                    const argList = args.split(',').map(arg => arg.trim());
                    return `pythonPrint(${argList.join(', ')}, {end: "${end}"})`;
                });

            try {
                // Execute the modified code
                eval(modifiedCode);

                // Handle any remaining current line
                if (currentLine) {
                    output.push(currentLine);
                }

                return output.join('\n');
            } catch (error) {
                throw new Error(`Code execution failed: ${error.message}`);
            }
        }
    </script>
</body>
</html>
